import { type Request, type Response } from "express";

import ReportService from "../services/report.service.js";

class Report {
  private readonly reportService = new ReportService();

  // ##########################################################################
  // # GET /reports/daily
  // ##########################################################################

  public getDailyReport = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const dailyReport = await this.reportService.getDailyReport(userId);

    res.success("Daily report fetched successfully", { dailyReport });
  };
}

export default Report;
