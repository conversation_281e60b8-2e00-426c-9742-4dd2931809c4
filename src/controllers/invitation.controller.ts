import { type Request, type Response } from "express";

import InvitationService from "../services/invitation.service.js";
import {
  type CreateInvitationRequest,
  type UpdateInvitationRequest,
  type DeleteInvitationRequest,
} from "../types/invitation.type.js";
import { INVITATION_STATUS } from "../utils/constants.js";

class Invitation {
  private readonly invitationService = new InvitationService();

  // ##########################################################################
  // # GET /invitations/sent
  // ##########################################################################

  public getSentInvitations = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const invitations = await this.invitationService.getSentInvitations(userId);

    res.success("Invitations fetched successfully", { invitations });
  };

  // ##########################################################################
  // # GET /invitations/received
  // ##########################################################################

  public getReceivedInvitations = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const invitations =
      await this.invitationService.getReceivedInvitations(userId);

    res.success("Invitations fetched successfully", { invitations });
  };

  // ##########################################################################
  // # POST /invitations
  // ##########################################################################

  public createInvitation = async (
    req: CreateInvitationRequest,
    res: Response,
  ) => {
    const userId = req.user!.id;
    const data = req.body;

    await this.invitationService.createInvitation(userId, data);

    res.success("Invitation created successfully", {}, 201);
  };

  // ##########################################################################
  // # PATCH /invitations/:id
  // ##########################################################################

  public updateInvitation = async (
    req: UpdateInvitationRequest,
    res: Response,
  ) => {
    const userId = req.user!.id;
    const id = Number(req.params.id);
    const { status } = req.body;

    if (status === INVITATION_STATUS.ACCEPTED) {
      const { invitee } = await this.invitationService.acceptInvitation(
        id,
        userId,
      );
      res.success(`Invitation accepted successfully`, { invitee });
    } else {
      await this.invitationService.rejectInvitation(id, userId);
      res.success(`Invitation rejected successfully`);
    }
  };

  // ##########################################################################
  // # DELETE /invitations/:id
  // ##########################################################################

  public deleteInvitation = async (
    req: DeleteInvitationRequest,
    res: Response,
  ) => {
    const userId = req.user!.id;
    const id = Number(req.params.id);

    await this.invitationService.deleteInvitation(id, userId);

    res.success("Invitation deleted successfully");
  };
}

export default Invitation;
