import { type Request, type Response } from "express";

import NotificationService from "../services/notification.service.js";
import { type UpdateNotificationRequest } from "../types/notification.type.js";

class Notification {
  private readonly notificationService = new NotificationService();

  // ##########################################################################
  // # GET /notifications
  // ##########################################################################

  public getNotifications = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const notifications =
      await this.notificationService.getNotifications(userId);

    res.success("Notifications fetched successfully", { notifications });
  };

  // ##########################################################################
  // # PATCH /notifications/:id
  // ##########################################################################

  public updateNotification = async (
    req: UpdateNotificationRequest,
    res: Response,
  ) => {
    const notificationId = Number(req.params.id);
    const userId = req.user!.id;

    await this.notificationService.updateNotification(notificationId, userId);

    res.success("Notification updated successfully");
  };
}

export default Notification;
