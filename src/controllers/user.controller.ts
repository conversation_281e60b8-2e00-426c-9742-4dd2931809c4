import { type Request, type Response } from "express";

import UserService from "../services/user.service.js";
import {
  type CreateUserRequest,
  type GetUserRequest,
  type UpdateMeRequest,
} from "../types/user.type.js";

class User {
  private readonly userService = new UserService();

  // ############################################################################
  // # GET /users/me
  // ############################################################################

  public getMe = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const me = await this.userService.getUser(userId);

    res.success("User fetched successfully", { me });
  };

  // ############################################################################
  // # GET /users/:id
  // ############################################################################

  public getUser = async (req: GetUserRequest, res: Response) => {
    const userId = req.params.id;

    const user = await this.userService.getUser(Number(userId));

    res.success("User fetched successfully", { user });
  };

  // ############################################################################
  // # GET /users
  // ############################################################################

  public getUsers = async (_req: Request, res: Response) => {
    const users = await this.userService.getUsers();

    res.success("Users fetched successfully", { users });
  };

  // ############################################################################
  // # POST /users
  // ############################################################################

  public createUser = async (req: CreateUserRequest, res: Response) => {
    const data = req.body;

    const user = await this.userService.createUser(data);

    res.success("User created successfully", { user });
  };

  // ############################################################################
  // # PATCH /users/me
  // ############################################################################

  public updateMe = async (req: UpdateMeRequest, res: Response) => {
    const userId = req.user!.id;
    const data = req.body;

    const me = await this.userService.updateMe(userId, data);

    res.success("User updated successfully", { me });
  };
}

export default User;
