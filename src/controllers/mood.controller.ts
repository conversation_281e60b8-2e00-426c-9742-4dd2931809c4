import { type Response } from "express";

import MoodService from "../services/mood.service.js";
import {
  type CreateMoodLogRequest,
  type CreateMoodReactionRequest,
  type GetMoodLogRequest,
} from "../types/mood.type.js";

class Mood {
  private readonly moodService = new MoodService();

  // ##########################################################################
  // # GET /moods/me
  // ##########################################################################

  public getMoodLog = async (req: GetMoodLogRequest, res: Response) => {
    const userId = req.user!.id;
    const { logDate } = req.query;

    const moodLog = await this.moodService.getMoodLog(userId, logDate);

    res.success("Mood log fetched successfully", { moodLog });
  };

  // ##########################################################################
  // # POST /moods
  // ##########################################################################

  public createMoodLog = async (req: CreateMoodLogRequest, res: Response) => {
    const userId = req.user!.id;
    const { moodTypeId } = req.body;

    const moodLog = await this.moodService.createMoodLog({
      moodTypeId,
      userId,
    });

    res.success("Mood log created successfully", { moodLog }, 201);
  };

  // ##########################################################################
  // # POST /moods/:id/reactions
  // ##########################################################################

  public createMoodReaction = async (
    req: CreateMoodReactionRequest,
    res: Response,
  ) => {
    const userId = req.user!.id;
    const moodLogId = Number(req.params.id);
    const { reactionTypeName } = req.body;

    const reaction = await this.moodService.createMoodReaction({
      moodLogId,
      reactionTypeName,
      userId,
    });

    res.success(
      `Mood ${reactionTypeName} reaction created successfully`,
      { reaction },
      201,
    );
  };
}

export default Mood;
