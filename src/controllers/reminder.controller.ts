import { type Request, type Response } from "express";

import ReminderService from "../services/reminder.service.js";
import { type CreateMoodReminderRequest } from "../types/reminder.type.js";

class Reminder {
  private readonly reminderService = new ReminderService();

  // ##########################################################################
  // # GET /reminders
  // ##########################################################################

  public getReminders = async (req: Request, res: Response) => {
    const userId = req.user!.id;

    const reminders = await this.reminderService.getReminders(userId);

    res.success("Reminders fetched successfully", { reminders });
  };

  // ##########################################################################
  // # POST /reminders/mood
  // ##########################################################################

  public createMoodReminder = async (
    req: CreateMoodReminderRequest,
    res: Response,
  ) => {
    const senderUserId = req.user!.id;
    const { receiverUserId } = req.body;

    await this.reminderService.createMoodReminder(senderUserId, receiverUserId);

    res.success("Mood reminder created successfully");
  };
}

export default Reminder;
