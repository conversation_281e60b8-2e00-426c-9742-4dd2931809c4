import { type Request, type Response } from "express";

import AuthService from "../services/auth.service.js";
import {
  type LoginRequest,
  type RefreshTokenRequest,
} from "../types/auth.type.js";

class Auth {
  private readonly authService = new AuthService();

  // ##########################################################################
  // # POST /login
  // ##########################################################################

  public login = async (req: LoginRequest, res: Response) => {
    const { phone } = req.body;

    const tokens = await this.authService.login(phone);

    res.success("Login successful", tokens);
  };

  // ##########################################################################
  // # POST /logout
  // ##########################################################################

  public logout = async (req: Request, res: Response) => {
    const userId = req.user!.id;
    const token = req.headers.authorization?.split(" ")[1];

    await this.authService.logout(userId, token);

    res.success("Logout successful");
  };

  // ##########################################################################
  // # POST /refresh
  // ##########################################################################

  public refreshToken = async (req: RefreshTokenRequest, res: Response) => {
    const { refreshToken } = req.body;

    const accessToken = await this.authService.refreshToken(refreshToken);

    res.success("Token refreshed successfully", { accessToken });
  };
}

export default Auth;
