import { type Request, type Response } from "express";

import MoodTypeService from "../services/mood-type.service.js";

class MoodType {
  private readonly moodTypeService = new MoodTypeService();

  // ##########################################################################
  // # GET /mood-types
  // ##########################################################################

  public getMoodTypes = async (_req: Request, res: Response) => {
    const moodTypes = await this.moodTypeService.getMoodTypes();

    res.success("Mood types fetched successfully", { moodTypes });
  };
}

export default MoodType;
