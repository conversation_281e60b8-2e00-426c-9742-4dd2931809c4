import express from "express";
import swaggerUi from "swagger-ui-express";

import cors from "./middlewares/cors.middleware.js";
import dbErrHandler from "./middlewares/db-err-handler.middleware.js";
import errHandler from "./middlewares/err-handler.middleware.js";
import notFound from "./middlewares/not-found.middleware.js";
import resHandler from "./middlewares/res-handler.middleware.js";
import routes from "./routes/index.js";
import swaggerSpec from "./swagger.js";
import env from "./utils/env.js";

class App {
  private readonly app = express();
  private readonly port = env.PORT;

  constructor() {
    this.middlewares();
    this.routes();
    this.swagger();
    this.notFound();
    this.errorHandlers();
    this.listen();
  }

  private middlewares() {
    this.app.use(resHandler);
    this.app.use(cors);
    this.app.use(express.json());
  }

  private routes() {
    routes.forEach((route) => {
      this.app.use(`/api/v1/${route.path}`, route.router);
    });
  }

  private swagger() {
    this.app.use("/api-docs", swaggerUi.serve, swaggerUi.setup(swaggerSpec));
  }

  private notFound() {
    this.app.use(notFound);
  }

  private errorHandlers() {
    this.app.use(dbErrHandler);
    this.app.use(errHandler);
  }

  private listen() {
    this.app.listen(this.port, () => {
      console.log(`
🟢  Listening on port ${this.port}
`);
    });
  }
}

export default App;

