import { type Request } from "express";

import {
  type LoginSchema,
  type RefreshTokenSchema,
} from "../validations/auth.validation.js";

// ############################################################################
// # POST /login
// ############################################################################

type LoginRequest = Request<unknown, unknown, LoginSchema["body"]>;

// ############################################################################
// # POST /refresh
// ############################################################################

type RefreshTokenRequest = Request<
  unknown,
  unknown,
  RefreshTokenSchema["body"]
>;

// ############################################################################
// # Exports
// ############################################################################

export type { LoginRequest, RefreshTokenRequest };
