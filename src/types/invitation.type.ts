import { type Request } from "express";

import {
  type CreateInvitationSchema,
  type UpdateInvitationSchema,
  type DeleteInvitationSchema,
} from "../validations/invitation.validation.js";

// ##########################################################################
// # POST /invitations
// ##########################################################################

type CreateInvitationRequest = Request<
  unknown,
  unknown,
  CreateInvitationSchema["body"]
>;

// ##########################################################################
// # PATCH /invitations/:id
// ##########################################################################

type UpdateInvitationRequest = Request<
  UpdateInvitationSchema["params"],
  unknown,
  UpdateInvitationSchema["body"]
>;

// ##########################################################################
// # DELETE /invitations/:id
// ##########################################################################

type DeleteInvitationRequest = Request<DeleteInvitationSchema["params"]>;

// ############################################################################
// # Exports
// ############################################################################

export type {
  CreateInvitationRequest,
  UpdateInvitationRequest,
  DeleteInvitationRequest,
};
