import { type Request } from "express";

import {
  CreateUserSchema,
  GetUserSchema,
  UpdateMeSchema,
} from "../validations/user.validation.js";

// ############################################################################
// # GET /users/:id
// ############################################################################

type GetUserRequest = Request<GetUserSchema["params"]>;

// ############################################################################
// # POST /users
// ############################################################################

type CreateUserRequest = Request<unknown, unknown, CreateUserSchema["body"]>;

// ############################################################################
// # PATCH /users/me
// ############################################################################

type UpdateMeRequest = Request<unknown, unknown, UpdateMeSchema["body"]>;

// ############################################################################
// # Exports
// ############################################################################

export type { CreateUserRequest, GetUserRequest, UpdateMeRequest };
