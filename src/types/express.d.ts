type UserRole = "admin" | "child" | "parent";

type UserPayload = {
  id: number;
  role: UserRole;
};

declare namespace Express {
  interface Request {
    user?: UserPayload;
  }

  interface Response {
    success: (
      message?: string,
      data?: Record<string, unknown>,
      statusCode?: number,
    ) => void;

    fail: (
      message?: string,
      data?: Record<string, unknown>,
      statusCode?: number,
    ) => void;
  }
}
