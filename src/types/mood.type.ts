import { type Request } from "express";

import {
  CreateMoodLogSchema,
  CreateMoodReactionSchema,
  GetMoodLogSchema,
} from "../validations/mood.validation.js";

// ############################################################################
// # GET /moods/me
// ############################################################################

type GetMoodLogRequest = Request<
  unknown,
  unknown,
  unknown,
  GetMoodLogSchema["query"]
>;

// ############################################################################
// # POST /moods
// ############################################################################

type CreateMoodLogRequest = Request<
  unknown,
  unknown,
  CreateMoodLogSchema["body"]
>;

// ############################################################################
// # POST /moods/:id/reactions
// ############################################################################

type CreateMoodReactionRequest = Request<
  CreateMoodReactionSchema["params"],
  unknown,
  CreateMoodReactionSchema["body"]
>;

// ############################################################################
// # Exports
// ############################################################################

export type {
  CreateMoodLogRequest,
  CreateMoodReactionRequest,
  GetMoodLogRequest,
};
