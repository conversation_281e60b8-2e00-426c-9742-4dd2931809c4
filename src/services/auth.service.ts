import bcrypt from "bcrypt";
import { eq } from "drizzle-orm";
import jwt from "jsonwebtoken";

import db from "../db/index.js";
import revokedTokens from "../db/schema/revoked-tokens.js";
import roleTypes from "../db/schema/role-types.js";
import users from "../db/schema/users.js";
import { USER_ROLES } from "../utils/constants.js";
import env from "../utils/env.js";
import {
  AppError,
  ForbiddenError,
  NotFoundError,
  UnauthorizedError,
} from "../utils/errors.js";

class Auth {
  // ##########################################################################
  // # Generate Access Token
  // ##########################################################################

  private generateAccessToken(payload: UserPayload) {
    return jwt.sign(payload, env.JWT_SECRET, {
      expiresIn: "7d",
    });
  }

  // ##########################################################################
  // # Generate Refresh Token
  // ##########################################################################

  private generateRefreshToken(payload: UserPayload) {
    return jwt.sign(payload, env.JWT_REFRESH_SECRET, {
      expiresIn: "7d",
    });
  }

  // ##########################################################################
  // # Refresh Token
  // ##########################################################################

  public async refreshToken(token: string) {
    if (!token) {
      throw new UnauthorizedError("Refresh token is required");
    }

    const decoded = jwt.verify(token, env.JWT_REFRESH_SECRET) as UserPayload;

    const [user] = await db
      .select({ id: users.id, refreshToken: users.refreshToken })
      .from(users)
      .where(eq(users.id, decoded.id));

    if (!user || !user.refreshToken) {
      throw new ForbiddenError("Invalid token. Please log in again");
    }

    const isTokenMatch = await bcrypt.compare(token, user.refreshToken);

    if (!isTokenMatch) {
      throw new ForbiddenError("Invalid token. Please log in again");
    }

    const newAccessToken = this.generateAccessToken({
      id: decoded.id,
      role: decoded.role,
    });

    return newAccessToken;
  }

  // ##########################################################################
  // # Update User Refresh Token
  // ##########################################################################

  private async updateUserRefreshToken(userId: number, token: string | null) {
    if (token) {
      const salt = await bcrypt.genSalt(10);
      const hashedToken = await bcrypt.hash(token, salt);

      await db
        .update(users)
        .set({ refreshToken: hashedToken })
        .where(eq(users.id, userId));
    } else {
      await db
        .update(users)
        .set({ refreshToken: null })
        .where(eq(users.id, userId));
    }
  }

  // ##########################################################################
  // # Login
  // ##########################################################################

  public async login(phone: string) {
    const [user] = await db
      .select({
        id: users.id,
        role: roleTypes.name,
      })
      .from(users)
      .where(eq(users.phone, phone))
      .leftJoin(roleTypes, eq(users.roleTypeId, roleTypes.id));

    if (!user) {
      throw new NotFoundError("User with this phone number not found");
    }

    if (USER_ROLES[user.role as keyof typeof USER_ROLES] === undefined) {
      throw new AppError("User has an invalid role", 500);
    }

    const payload: UserPayload = { id: user.id, role: user.role as UserRole };
    const accessToken = this.generateAccessToken(payload);
    const refreshToken = this.generateRefreshToken(payload);

    await this.updateUserRefreshToken(user.id, refreshToken);

    return { accessToken, refreshToken };
  }

  // ##########################################################################
  // # Logout
  // ##########################################################################

  public async logout(userId: number, token?: string) {
    await this.updateUserRefreshToken(userId, null);
    if (token) {
      await db.insert(revokedTokens).values({ token, userId });
    }
  }
}

export default Auth;
