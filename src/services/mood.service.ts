import { and, eq } from "drizzle-orm";

import db from "../db/index.js";
import moodLogs from "../db/schema/mood-logs.js";
import reactionTypes from "../db/schema/reaction-types.js";
import reactions from "../db/schema/reactions.js";
import { BusinessLogicError, NotFoundError } from "../utils/errors.js";

import LeafService from "./leaf.service.js";
import NotificationService from "./notification.service.js";

class Mood {
  private readonly leafService = new LeafService();
  private readonly notificationService = new NotificationService();

  // ##########################################################################
  // # Get Mood Log
  // ##########################################################################

  public async getMoodLog(userId: number, logDate: string) {
    const [moodLog] = await db
      .select()
      .from(moodLogs)
      .where(and(eq(moodLogs.userId, userId), eq(moodLogs.logDate, logDate)));

    if (!moodLog) {
      throw new NotFoundError("Mood log not found");
    }

    return moodLog;
  }

  // ##########################################################################
  // # Create Mood Log
  // ##########################################################################

  public async createMoodLog(data: { moodTypeId: number; userId: number }) {
    return db.transaction(async (tx) => {
      const [moodLog] = await tx.insert(moodLogs).values({
        ...data,
        logDate: new Date().toISOString().slice(0, 10),
      });

      await this.leafService.awardLeaves(tx, {
        earnedLeaves: 1,
        leafSrcActionName: "mood",
        leafSrcTableName: "mood_logs",
        srcId: moodLog.insertId,
        userId: data.userId,
      });

      const [newMoodLog] = await tx
        .select()
        .from(moodLogs)
        .where(eq(moodLogs.id, moodLog.insertId));

      return newMoodLog;
    });
  }

  // ##########################################################################
  // # Create Mood Reaction
  // ##########################################################################

  public async createMoodReaction(data: {
    moodLogId: number;
    reactionTypeName: "seen" | "heart";
    userId: number;
  }) {
    const { moodLogId, reactionTypeName, userId } = data;

    return db.transaction(async (tx) => {
      const [moodLog] = await tx
        .select({ userId: moodLogs.userId })
        .from(moodLogs)
        .where(eq(moodLogs.id, moodLogId));

      if (!moodLog) {
        throw new NotFoundError("Mood log not found");
      }

      if (moodLog.userId === userId) {
        throw new BusinessLogicError(
          "Users cannot react to their own mood logs",
          403,
        );
      }

      const [reactionType] = await tx
        .select({ id: reactionTypes.id })
        .from(reactionTypes)
        .where(eq(reactionTypes.name, reactionTypeName));

      if (!reactionType) {
        throw new Error("Reaction type not found");
      }

      const [reaction] = await tx.insert(reactions).values({
        moodLogId,
        reactionTypeId: reactionType.id,
        userId,
      });

      if (reactionTypeName === "heart") {
        await this.leafService.awardLeaves(tx, {
          userId,
          earnedLeaves: 1,
          leafSrcTableName: "reactions",
          leafSrcActionName: "heart",
          srcId: reaction.insertId,
        });
      }

      const [newReaction] = await tx
        .select()
        .from(reactions)
        .where(eq(reactions.id, reaction.insertId));

      await this.notificationService.createNotification({
        notifSrcTableName: "reactions",
        notifTypeName: reactionTypeName,
        receiverUserId: moodLog.userId,
        senderUserId: userId,
        srcId: newReaction.id,
      });

      return newReaction;
    });
  }
}

export default Mood;
