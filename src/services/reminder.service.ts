import { and, eq } from "drizzle-orm";

import db from "../db/index.js";
import familyRels from "../db/schema/family-rels.js";
import moodLogs from "../db/schema/mood-logs.js";
import reminderTypes from "../db/schema/reminder-types.js";
import reminders from "../db/schema/reminders.js";
import users from "../db/schema/users.js";
import { BusinessLogicError, NotFoundError } from "../utils/errors.js";

class Reminder {
  // ##########################################################################
  // # Get Reminders
  // ##########################################################################

  public async getReminders(userId: number) {
    const userReminders = await db
      .select({
        id: reminders.id,
        reminderDate: reminders.reminderDate,
        createdAt: reminders.createdAt,
        sender: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        reminderType: {
          id: reminderTypes.id,
          name: reminderTypes.name,
        },
      })
      .from(reminders)
      .where(eq(reminders.receiverUserId, userId))
      .innerJoin(users, eq(reminders.senderUserId, users.id))
      .innerJoin(reminderTypes, eq(reminders.reminderTypeId, reminderTypes.id));

    return userReminders;
  }

  // ##########################################################################
  // # Create Mood Reminder
  // ##########################################################################

  public async createMoodReminder(
    senderUserId: number,
    receiverUserId: number,
  ) {
    const [familyRel] = await db
      .select()
      .from(familyRels)
      .where(
        and(
          eq(familyRels.fromUserId, senderUserId),
          eq(familyRels.toUserId, receiverUserId),
        ),
      );

    if (!familyRel) {
      throw new NotFoundError("Family relationship not found");
    }

    const today = new Date().toISOString().slice(0, 10);

    const [moodLog] = await db
      .select()
      .from(moodLogs)
      .where(
        and(eq(moodLogs.userId, receiverUserId), eq(moodLogs.logDate, today)),
      );

    if (moodLog) {
      throw new BusinessLogicError(
        "The user has already registered his/her mood today",
      );
    }

    const [moodReminderType] = await db
      .select()
      .from(reminderTypes)
      .where(eq(reminderTypes.name, "mood"));

    const [reminder] = await db.insert(reminders).values({
      senderUserId,
      receiverUserId,
      reminderTypeId: moodReminderType.id,
      reminderDate: new Date().toISOString().slice(0, 10),
    });

    return reminder;
  }
}

export default Reminder;
