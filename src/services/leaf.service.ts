import { eq, sql } from "drizzle-orm";

import leafLogs from "../db/schema/leaf-logs.js";
import leafSrcActions from "../db/schema/leaf-src-actions.js";
import leafSrcTables from "../db/schema/leaf-src-tables.js";
import users from "../db/schema/users.js";
import { BusinessLogicError } from "../utils/errors.js";

class Leaf {
  // ##########################################################################
  // # Award Leaves
  // ##########################################################################

  public async awardLeaves(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    db: any,
    data: {
      earnedLeaves: number;
      leafSrcActionName: "mood" | "heart";
      leafSrcTableName: "mood_logs" | "reactions";
      srcId: number;
      userId: number;
    },
  ) {
    const { earnedLeaves, leafSrcActionName, leafSrcTableName, srcId, userId } =
      data;

    const [leafSrcTableId] = await db
      .select({ id: leafSrcTables.id })
      .from(leafSrcTables)
      .where(eq(leafSrcTables.name, leafSrcTableName));

    const [leafSrcActionId] = await db
      .select({ id: leafSrcActions.id })
      .from(leafSrcActions)
      .where(eq(leafSrcActions.name, leafSrcActionName));

    if (!leafSrcTableId || !leafSrcActionId) {
      throw new Error(
        `Leaf source table or leaf source action not found. Table: ${leafSrcTableName}, Action: ${leafSrcActionName}`,
      );
    }

    await db.insert(leafLogs).values({
      earnedLeaves,
      leafSrcActionId: leafSrcActionId.id,
      leafSrcTableId: leafSrcTableId.id,
      srcId,
      userId,
    });

    const [updatedUser] = await db
      .update(users)
      .set({
        totalLeavesCount: sql`${users.totalLeavesCount} + ${earnedLeaves}`,
      })
      .where(eq(users.id, userId));

    if (updatedUser.affectedRows === 0) {
      throw new BusinessLogicError("User not found to award leaves to", 404);
    }
  }
}

export default Leaf;
