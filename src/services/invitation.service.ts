import { and, eq, gte, or, inArray } from "drizzle-orm";

import db from "../db/index.js";
import familyRelTypes from "../db/schema/family-rel-types.js";
import familyRels from "../db/schema/family-rels.js";
import invitations, {
  type InsertInvitation,
} from "../db/schema/invitations.js";
import users from "../db/schema/users.js";
import {
  HOUSEHOLD_MAX_CAPACITY,
  INVITATION_COOLDOWN_HOURS,
  INVITATION_EXPIRATION_HOURS,
  INVITATION_STATUS,
  USER_GENDER,
} from "../utils/constants.js";
import {
  BusinessLogicError,
  ForbiddenError,
  NotFoundError,
} from "../utils/errors.js";
import { formatDate } from "../utils/index.js";

class Invitation {
  // ##########################################################################
  // # Get Sent Invitations
  // ##########################################################################

  public async getSentInvitations(userId: number) {
    return db
      .select()
      .from(invitations)
      .where(
        and(
          inArray(invitations.invitationStatusType, [
            INVITATION_STATUS.JOINED,
            INVITATION_STATUS.PENDING,
          ]),
          eq(invitations.userId, userId),
        ),
      );
  }

  // ##########################################################################
  // # Get Received Invitations
  // ##########################################################################

  public async getReceivedInvitations(userId: number) {
    const [user] = await db.select().from(users).where(eq(users.id, userId));

    const invitationList = await db
      .select({
        id: invitations.id,
        invitationStatusType: invitations.invitationStatusType,
        expiresAt: invitations.expiresAt,
        createdAt: invitations.createdAt,
        sender: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
      })
      .from(invitations)
      .where(
        and(
          inArray(invitations.invitationStatusType, [
            INVITATION_STATUS.PENDING,
          ]),
          eq(invitations.inviteePhone, user.phone),
        ),
      )
      .innerJoin(users, eq(invitations.userId, users.id));

    return invitationList;
  }

  // ##########################################################################
  // # Create Invitation
  // ##########################################################################

  public async createInvitation(
    userId: number,
    {
      familyRelTypeId,
      inviteePhone,
    }: Pick<InsertInvitation, "familyRelTypeId" | "inviteePhone">,
  ) {
    const [invitee] = await db
      .select()
      .from(users)
      .where(eq(users.phone, inviteePhone));

    if (!invitee) {
      throw new NotFoundError("Invitee not found");
    }

    if (userId === invitee.id) {
      throw new BusinessLogicError("You can't invite yourself");
    }

    const userCapacity = await this.getUserCapacity(userId);

    if (userCapacity >= HOUSEHOLD_MAX_CAPACITY) {
      throw new BusinessLogicError("You have reached your capacity limit");
    }

    const inviteeCapacity = await this.getUserCapacity(invitee.id);

    if (inviteeCapacity >= HOUSEHOLD_MAX_CAPACITY) {
      throw new BusinessLogicError("Invitee has reached their capacity limit");
    }

    const now = new Date();
    const cooldownDate = new Date(
      now.getTime() - INVITATION_COOLDOWN_HOURS * 60 * 60 * 1000,
    ).toISOString();

    const [user] = await db.select().from(users).where(eq(users.id, userId));

    const existingInvitation = await db.query.invitations.findFirst({
      where: or(
        and(
          inArray(invitations.invitationStatusType, [
            INVITATION_STATUS.ACCEPTED,
            INVITATION_STATUS.JOINED,
            INVITATION_STATUS.PENDING,
          ]),
          or(
            and(
              eq(invitations.userId, userId),
              eq(invitations.inviteePhone, invitee.phone),
            ),
            and(
              eq(invitations.userId, invitee.id),
              eq(invitations.inviteePhone, user.phone),
            ),
          ),
        ),
        and(
          inArray(invitations.invitationStatusType, [
            INVITATION_STATUS.REJECTED,
          ]),
          or(
            and(
              eq(invitations.userId, userId),
              eq(invitations.inviteePhone, invitee.phone),
            ),
            and(
              eq(invitations.userId, invitee.id),
              eq(invitations.inviteePhone, user.phone),
            ),
          ),
          gte(invitations.updatedAt, cooldownDate),
        ),
      ),
    });

    if (existingInvitation) {
      switch (existingInvitation.invitationStatusType) {
        case INVITATION_STATUS.ACCEPTED:
        case INVITATION_STATUS.JOINED:
          throw new BusinessLogicError(
            "This user is already in your household",
          );

        case INVITATION_STATUS.PENDING:
          throw new BusinessLogicError(
            "You already have a pending invitation with this user",
          );

        case INVITATION_STATUS.REJECTED:
          throw new BusinessLogicError(
            `You must wait ${INVITATION_COOLDOWN_HOURS} hours before sending another invitation to this user`,
          );
      }
    }

    const expiresAt = new Date(
      now.getTime() + INVITATION_EXPIRATION_HOURS * 60 * 60 * 1000,
    );

    const [invitation] = await db.insert(invitations).values({
      expiresAt: formatDate(expiresAt),
      familyRelTypeId,
      inviteePhone,
      userId,
    });

    const newInvitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, invitation.insertId),
    });

    return newInvitation;
  }

  // ##########################################################################
  // # Accept Invitation
  // ##########################################################################

  public async acceptInvitation(invitationId: number, inviteeId: number) {
    const invitation = await this.findInvitation(invitationId, inviteeId);

    const inviterCapacity = await this.getUserCapacity(invitation.userId);

    if (inviterCapacity >= HOUSEHOLD_MAX_CAPACITY) {
      throw new BusinessLogicError("Inviter has reached their capacity limit");
    }

    const inviteeCapacity = await this.getUserCapacity(inviteeId);

    if (inviteeCapacity >= HOUSEHOLD_MAX_CAPACITY) {
      throw new BusinessLogicError("You have reached your capacity limit");
    }

    await db.transaction(async (tx) => {
      await tx
        .update(invitations)
        .set({ invitationStatusType: INVITATION_STATUS.ACCEPTED })
        .where(eq(invitations.id, invitationId));

      const [user] = await tx
        .select({ gender: users.gender })
        .from(users)
        .where(eq(users.id, invitation.userId));

      const [familyRelType] = await tx
        .select()
        .from(familyRelTypes)
        .where(eq(familyRelTypes.id, invitation.familyRelTypeId));

      const inverseFamilyRelTypeId =
        user.gender === USER_GENDER.MALE
          ? familyRelType.inverse_male
          : familyRelType.inverse_female;

      await tx.insert(familyRels).values([
        {
          fromUserId: invitation.userId,
          toUserId: inviteeId,
          familyRelTypeId: invitation.familyRelTypeId,
        },
        {
          fromUserId: inviteeId,
          toUserId: invitation.userId,
          familyRelTypeId: inverseFamilyRelTypeId,
        },
      ]);
    });

    const [invitee] = await db
      .select({
        id: users.id,
        firstName: users.firstName,
        lastName: users.lastName,
      })
      .from(users)
      .where(eq(users.id, inviteeId));

    return { invitee };
  }

  // ##########################################################################
  // # Reject Invitation
  // ##########################################################################

  public rejectInvitation = async (invitationId: number, inviteeId: number) => {
    await this.findInvitation(invitationId, inviteeId);

    const now = new Date();
    const expiresAt = new Date(
      now.getTime() + INVITATION_EXPIRATION_HOURS * 60 * 60 * 1000,
    );

    await db
      .update(invitations)
      .set({
        invitationStatusType: INVITATION_STATUS.REJECTED,
        expiresAt: formatDate(expiresAt),
      })
      .where(eq(invitations.id, invitationId));
  };

  // ##########################################################################
  // # Delete Invitation
  // ##########################################################################

  public deleteInvitation = async (invitationId: number, userId: number) => {
    const invitation = await db.query.invitations.findFirst({
      where: eq(invitations.id, invitationId),
    });

    if (!invitation) {
      throw new NotFoundError("Invitation not found");
    }

    if (invitation.invitationStatusType === INVITATION_STATUS.PENDING) {
      if (invitation.userId !== userId) {
        throw new ForbiddenError(
          "You are not authorized to delete this invitation",
        );
      }

      await db
        .update(invitations)
        .set({ invitationStatusType: INVITATION_STATUS.DELETED })
        .where(eq(invitations.id, invitationId));
    }

    if (
      invitation.invitationStatusType === INVITATION_STATUS.ACCEPTED ||
      invitation.invitationStatusType === INVITATION_STATUS.JOINED
    ) {
      const invitee = await db.query.users.findFirst({
        where: eq(users.phone, invitation.inviteePhone),
      });

      if (invitation.userId !== userId && invitee?.id !== userId) {
        throw new ForbiddenError(
          "You are not authorized to delete this invitation",
        );
      }

      await db.transaction(async (tx) => {
        await tx
          .update(invitations)
          .set({ invitationStatusType: INVITATION_STATUS.DELETED })
          .where(eq(invitations.id, invitationId));

        await tx
          .delete(familyRels)
          .where(
            or(
              and(
                eq(familyRels.fromUserId, invitation.userId),
                eq(familyRels.toUserId, invitee!.id),
              ),
              and(
                eq(familyRels.fromUserId, invitee!.id),
                eq(familyRels.toUserId, invitation.userId),
              ),
            ),
          );
      });
    }
  };

  // ##########################################################################
  // # Get User Capacity (Utility)
  // ##########################################################################

  private getUserCapacity = async (userId: number) => {
    const activeInvitations = await db
      .select({ id: invitations.id })
      .from(invitations)
      .where(
        and(
          inArray(invitations.invitationStatusType, [
            INVITATION_STATUS.ACCEPTED,
            INVITATION_STATUS.JOINED,
            INVITATION_STATUS.PENDING,
          ]),
          eq(invitations.userId, userId),
        ),
      );

    return activeInvitations.length;
  };

  // ##########################################################################
  // # Find Invitation (Utility)
  // ##########################################################################

  private findInvitation = async (id: number, inviteeId: number) => {
    const invitation = await db.query.invitations.findFirst({
      where: and(
        eq(invitations.id, id),
        eq(
          invitations.inviteePhone,
          (await db.query.users.findFirst({ where: eq(users.id, inviteeId) }))
            ?.phone ?? "",
        ),
      ),
    });

    if (!invitation) {
      throw new NotFoundError("Invitation not found");
    }

    if (invitation.invitationStatusType !== INVITATION_STATUS.PENDING) {
      throw new BusinessLogicError(
        `Invitation is already ${invitation.invitationStatusType}`,
      );
    }

    return invitation;
  };
}

export default Invitation;
