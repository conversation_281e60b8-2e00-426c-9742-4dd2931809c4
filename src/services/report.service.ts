import { and, eq, gte, sql } from "drizzle-orm";

import db from "../db/index.js";
import leafLogs from "../db/schema/leaf-logs.js";
import moodLogs from "../db/schema/mood-logs.js";
import moodTypes from "../db/schema/mood-types.js";

class Report {
  // ##########################################################################
  // # Get Daily Report
  // ##########################################################################

  public async getDailyReport(userId: number) {
    const today = new Date();
    const dayOfWeek = today.getDay();
    const startDate = new Date(today);
    startDate.setDate(today.getDate() - dayOfWeek);
    startDate.setHours(0, 0, 0, 0);

    const moodLogsPromise = db
      .select({
        logDate: moodLogs.logDate,
        moodType: moodTypes.name,
      })
      .from(moodLogs)
      .where(
        and(
          eq(moodLogs.userId, userId),
          gte(moodLogs.logDate, startDate.toISOString().slice(0, 10)),
        ),
      )
      .innerJoin(moodTypes, eq(moodLogs.moodTypeId, moodTypes.id));

    const leafLogsPromise = db
      .select({
        date: sql<string>`DATE(created_at)`,
        totalLeaves: sql<number>`SUM(earned_leaves)`,
      })
      .from(leafLogs)
      .where(
        and(
          eq(leafLogs.userId, userId),
          gte(leafLogs.createdAt, startDate.toISOString()),
        ),
      )
      .groupBy(sql`DATE(created_at)`);

    const [moodLogsResult, leafLogsResult] = await Promise.all([
      moodLogsPromise,
      leafLogsPromise,
    ]);

    const dailyReports = Array.from({ length: 7 }, (_, i) => {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      const dateString = date.toISOString().slice(0, 10);

      const moodLog = moodLogsResult.find((log) => log.logDate === dateString);

      const earnedLeaves =
        leafLogsResult.find((log) => log.date === dateString)?.totalLeaves || 0;

      return {
        date: dateString,
        moodType: moodLog?.moodType || null,
        earnedLeaves,
      };
    });

    return dailyReports;
  }
}

export default Report;
