import { eq } from "drizzle-orm";

import db from "../db/index.js";
import users, { type InsertUser } from "../db/schema/users.js";
import { NotFoundError } from "../utils/errors.js";

class User {
  // ############################################################################
  // # Get User
  // ############################################################################

  public async getUser(id: number) {
    const [user] = await db.select().from(users).where(eq(users.id, id));

    if (!user) {
      throw new NotFoundError("User not found");
    }

    return user;
  }

  // ############################################################################
  // # Get Users
  // ############################################################################

  public async getUsers() {
    const allUsers = await db.select().from(users).orderBy(users.id);

    return allUsers;
  }

  // ############################################################################
  // # Create User
  // ############################################################################

  public async createUser(data: InsertUser) {
    const [user] = await db.insert(users).values(data);

    const [newUser] = await db
      .select()
      .from(users)
      .where(eq(users.id, user.insertId));

    return newUser;
  }

  // ############################################################################
  // # Update Me
  // ############################################################################

  public async updateMe(id: number, data: Partial<InsertUser>) {
    await db.update(users).set(data).where(eq(users.id, id));

    const [updatedMe] = await db.select().from(users).where(eq(users.id, id));

    return updatedMe;
  }
}

export default User;
