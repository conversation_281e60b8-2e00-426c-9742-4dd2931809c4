import { and, eq } from "drizzle-orm";

import db from "../db/index.js";
import notifSrcTables from "../db/schema/notif-src-tables.js";
import notifTypes from "../db/schema/notif-types.js";
import notifs from "../db/schema/notifs.js";
import users from "../db/schema/users.js";

class Notification {
  // ############################################################################
  // # Get Notifications
  // ############################################################################

  public async getNotifications(userId: number) {
    const userNotifications = await db
      .select({
        id: notifs.id,
        isRead: notifs.isRead,
        createdAt: notifs.createdAt,
        sender: {
          id: users.id,
          firstName: users.firstName,
          lastName: users.lastName,
        },
        notificationType: {
          id: notifTypes.id,
          name: notifTypes.name,
        },
      })
      .from(notifs)
      .where(eq(notifs.receiverUserId, userId))
      .innerJoin(users, eq(notifs.senderUserId, users.id))
      .innerJoin(notifTypes, eq(notifs.notifTypeId, notifTypes.id));

    return userNotifications;
  }

  // ##########################################################################
  // # Create Notification
  // ##########################################################################

  public async createNotification(data: {
    notifSrcTableName: string;
    notifTypeName: string;
    receiverUserId: number;
    senderUserId: number;
    srcId: number;
  }) {
    const {
      notifSrcTableName,
      notifTypeName,
      receiverUserId,
      senderUserId,
      srcId,
    } = data;

    const [notifSrcTable] = await db
      .select()
      .from(notifSrcTables)
      .where(eq(notifSrcTables.name, notifSrcTableName));

    const [notifType] = await db
      .select()
      .from(notifTypes)
      .where(eq(notifTypes.name, notifTypeName));

    const [notification] = await db.insert(notifs).values({
      notifSrcTableId: notifSrcTable.id,
      notifTypeId: notifType.id,
      receiverUserId,
      senderUserId,
      srcId,
    });

    return notification;
  }

  // ##########################################################################
  // # Update Notification
  // ##########################################################################

  public async updateNotification(notificationId: number, userId: number) {
    const [notification] = await db
      .update(notifs)
      .set({ isRead: true })
      .where(
        and(eq(notifs.id, notificationId), eq(notifs.receiverUserId, userId)),
      );

    return notification;
  }
}

export default Notification;
