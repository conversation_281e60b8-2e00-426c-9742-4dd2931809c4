import z from "zod";

// ############################################################################
// # PATCH /notifications/:id
// ############################################################################

const updateNotificationSchema = z.object({
  params: z.object({
    id: z.string().regex(/^\d+$/, "Notification ID must be a numeric string"),
  }),
});

type UpdateNotificationSchema = z.infer<typeof updateNotificationSchema>;

// ############################################################################
// # Exports
// ############################################################################

export { updateNotificationSchema };
export type { UpdateNotificationSchema };
