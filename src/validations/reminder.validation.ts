import z from "zod";

// ############################################################################
// # POST /reminders/mood
// ############################################################################

const createMoodReminderSchema = z.object({
  body: z.object({
    receiverUserId: z.number().int().positive(),
  }),
});

type CreateMoodReminderSchema = z.infer<typeof createMoodReminderSchema>;

// ############################################################################
// # Exports
// ############################################################################

export { createMoodReminderSchema };
export type { CreateMoodReminderSchema };
