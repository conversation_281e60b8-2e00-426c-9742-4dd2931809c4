import z from "zod";

// ############################################################################
// # POST /login
// ############################################################################

const loginSchema = z.object({
  body: z.object({
    phone: z.string("Phone number is required"),
  }),
});

type LoginSchema = z.infer<typeof loginSchema>;

// ############################################################################
// # POST /refresh
// ############################################################################

const refreshTokenSchema = z.object({
  body: z.object({
    refreshToken: z.string("Refresh token is required"),
  }),
});

type RefreshTokenSchema = z.infer<typeof refreshTokenSchema>;

// ############################################################################
// # Exports
// ############################################################################

export { loginSchema, refreshTokenSchema };
export type { LoginSchema, RefreshTokenSchema };
