import z from "zod";

// ##########################################################################
// # POST /invitations
// ##########################################################################

const createInvitationSchema = z.object({
  body: z.object({
    familyRelTypeId: z.number(),
    inviteePhone: z.string(),
  }),
});

type CreateInvitationSchema = z.infer<typeof createInvitationSchema>;

// ##########################################################################
// # PATCH /invitations/:id
// ##########################################################################

const updateInvitationSchema = z.object({
  body: z.object({
    status: z.enum(["accepted", "rejected"]),
  }),

  params: z.object({
    id: z.string(),
  }),
});

type UpdateInvitationSchema = z.infer<typeof updateInvitationSchema>;

// ##########################################################################
// # DELETE /invitations/:id
// ##########################################################################

const deleteInvitationSchema = z.object({
  params: z.object({
    id: z.string(),
  }),
});

type DeleteInvitationSchema = z.infer<typeof deleteInvitationSchema>;

// ############################################################################
// # Exports
// ############################################################################

export {
  createInvitationSchema,
  updateInvitationSchema,
  deleteInvitationSchema,
};
export type {
  CreateInvitationSchema,
  UpdateInvitationSchema,
  DeleteInvitationSchema,
};
