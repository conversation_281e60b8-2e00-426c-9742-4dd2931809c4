import z from "zod";

import { insertMoodLogSchema } from "../db/schema/mood-logs.js";

// ############################################################################
// # GET /moods/me
// ############################################################################

const getMoodLogSchema = z.object({
  query: z.object({
    logDate: z
      .string()
      .regex(/^\d{4}-\d{2}-\d{2}$/, "Log date must be in YYYY-MM-DD format"),
  }),
});

type GetMoodLogSchema = z.infer<typeof getMoodLogSchema>;

// ############################################################################
// # POST /moods
// ############################################################################

const createMoodLogSchema = z.object({
  body: insertMoodLogSchema.omit({
    id: true,
    userId: true,
    logDate: true,
    createdAt: true,
  }),
});

type CreateMoodLogSchema = z.infer<typeof createMoodLogSchema>;

// ############################################################################
// # POST /moods/:id/reactions
// ############################################################################

const createMoodReactionSchema = z.object({
  body: z.object({
    reactionTypeName: z.enum(["seen", "heart"], {
      message: "Invalid reaction type",
    }),
  }),

  params: z.object({
    id: z.string().regex(/^\d+$/, "Mood log ID must be a numeric string"),
  }),
});

type CreateMoodReactionSchema = z.infer<typeof createMoodReactionSchema>;

// ############################################################################
// # Exports
// ############################################################################

export { createMoodLogSchema, createMoodReactionSchema, getMoodLogSchema };
export type { CreateMoodLogSchema, CreateMoodReactionSchema, GetMoodLogSchema };
