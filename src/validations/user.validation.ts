import z from "zod";

import { insertUserSchema } from "../db/schema/users.js";

// ############################################################################
// # GET /users/:id
// ############################################################################

const getUserSchema = z.object({
  params: z.object({
    id: z.string().regex(/^\d+$/, "User ID must be a numeric string"),
  }),
});

type GetUserSchema = z.infer<typeof getUserSchema>;

// ############################################################################
// # POST /users
// ############################################################################

const createUserSchema = z.object({
  body: insertUserSchema.omit({
    id: true,
    totalLeavesCount: true,
    createdAt: true,
    updatedAt: true,
  }),
});

type CreateUserSchema = z.infer<typeof createUserSchema>;

// ############################################################################
// # PATCH /users/me
// ############################################################################

const updateMeSchema = z.object({
  body: insertUserSchema
    .omit({
      id: true,
      refreshToken: true,
      roleTypeId: true,
      totalLeavesCount: true,
      createdAt: true,
      updatedAt: true,
    })
    .partial(),
});

type UpdateMeSchema = z.infer<typeof updateMeSchema>;

// ############################################################################
// # Exports
// ############################################################################

export { createUserSchema, getUserSchema, updateMeSchema };
export type { CreateUserSchema, GetUserSchema, UpdateMeSchema };
