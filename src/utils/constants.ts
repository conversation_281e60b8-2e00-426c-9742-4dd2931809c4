const HOUSEHOLD_MAX_CAPACITY = 4;
const INVITATION_EXPIRATION_HOURS = 72;
const INVITATION_COOLDOWN_HOURS = 72;

const INVITATION_STATUS = {
  ACCEPTED: "accepted",
  DELETED: "deleted",
  EXPIRED: "expired",
  JOINED: "joined",
  PENDING: "pending",
  REJECTED: "rejected",
} as const;

const USER_GENDER = {
  MALE: "آقا",
  FEMALE: "خانم",
} as const;

const USER_ROLES = { ادمین: "admin", ریشه: "parent", فرزند: "child" } as const;

type InvitationStatus =
  (typeof INVITATION_STATUS)[keyof typeof INVITATION_STATUS];

export {
  HOUSEHOLD_MAX_CAPACITY,
  INVITATION_COOLDOWN_HOURS,
  INVITATION_EXPIRATION_HOURS,
  INVITATION_STATUS,
  USER_GENDER,
  USER_ROLES,
};
export type { InvitationStatus };
