class AppError extends Error {
  public readonly statusCode: number;

  constructor(message: string, statusCode: number) {
    super(message);

    this.name = this.constructor.name;
    this.statusCode = statusCode;

    Error.captureStackTrace(this, this.constructor);
  }
}

class BusinessLogicError extends AppError {
  constructor(message: string, statusCode = 400) {
    super(message, statusCode);
  }
}

class DatabaseError extends AppError {
  public readonly code?: string;
  public readonly field?: string;

  constructor(
    message: string,
    statusCode = 500,
    code?: string,
    field?: string,
  ) {
    super(message, statusCode);

    this.code = code;
    this.field = field;
  }
}

class ForbiddenError extends AppError {
  constructor(message: string) {
    super(message, 403);
  }
}

class NotFoundError extends AppError {
  constructor(message: string) {
    super(message, 404);
  }
}

class UnauthorizedError extends AppError {
  constructor(message: string) {
    super(message, 401);
  }
}

export {
  AppError,
  BusinessLogicError,
  DatabaseError,
  ForbiddenError,
  NotFoundError,
  UnauthorizedError,
};
