import z from "zod";

const schema = z.object({
  DB_HOST: z.string(),
  DB_NAME: z.string(),
  DB_PASSWORD: z.string(),
  DB_PORT: z.string(),
  DB_USER: z.string(),
  DB_URL: z.string(),
  JWT_REFRESH_SECRET: z.string(),
  JWT_SECRET: z.string(),
  PORT: z.string(),
});

const result = schema.safeParse(process.env);

if (!result.success) {
  console.error(z.flattenError(result.error));

  throw new Error("Invalid environment variables");
}

export default result.data;
