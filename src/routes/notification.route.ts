import { Router } from "express";

import NotificationController from "../controllers/notification.controller.js";
import protect from "../middlewares/auth.middleware.js";
import validation from "../middlewares/validation.middleware.js";
import { updateNotificationSchema } from "../validations/notification.validation.js";

/**
 * @swagger
 * tags:
 *   name: Notifications
 *   description: Notification management
 */

class Notification {
  private readonly notificationController = new NotificationController();
  public readonly router = Router();

  constructor() {
    this.GET();
    this.PATCH();
  }

  private GET() {
    /**
     * @swagger
     * /notifications:
     *   get:
     *     summary: Get all notifications for the logged-in user
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get("/", protect, this.notificationController.getNotifications);
  }

  private PATCH() {
    /**
     * @swagger
     * /notifications/{id}:
     *   patch:
     *     summary: Mark a notification as read
     *     tags: [Notifications]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: integer
     *         required: true
     *         description: Notification ID
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.patch(
      "/:id",
      protect,
      validation(updateNotificationSchema),
      this.notificationController.updateNotification,
    );
  }
}

export default Notification;
