import { Router } from "express";

import ReportController from "../controllers/report.controller.js";
import protect from "../middlewares/auth.middleware.js";

/**
 * @swagger
 * tags:
 *   name: Reports
 *   description: Report management
 */

class Report {
  private readonly reportController = new ReportController();
  public readonly router = Router();

  constructor() {
    this.GET();
  }

  private GET() {
    /**
     * @swagger
     * /reports/daily:
     *   get:
     *     summary: Get daily report of earned leaves and mood registration for the current week
     *     tags: [Reports]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get("/daily", protect, this.reportController.getDailyReport);
  }
}

export default Report;
