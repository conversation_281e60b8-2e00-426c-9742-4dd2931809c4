import { Router } from "express";

import Mood<PERSON>ontroller from "../controllers/mood.controller.js";
import protect from "../middlewares/auth.middleware.js";
import validate from "../middlewares/validation.middleware.js";
import {
  createMoodLogSchema,
  createMoodReactionSchema,
  getMoodLogSchema,
} from "../validations/mood.validation.js";

/**
 * @swagger
 * tags:
 *   name: Moods
 *   description: Mood management
 */

class Mood {
  private readonly moodController = new MoodController();
  public readonly router = Router();

  constructor() {
    this.GET();
    this.POST();
  }

  private GET() {
    /**
     * @swagger
     * /moods/me:
     *   get:
     *     summary: Get mood log for a specific date
     *     tags: [Moods]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: query
     *         name: logDate
     *         schema:
     *           type: string
     *           format: date
     *         required: true
     *         description: The date of the mood log (YYYY-MM-DD)
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.get(
      "/me",
      protect,
      validate(getMoodLogSchema),
      this.moodController.getMoodLog,
    );
  }

  private POST() {
    /**
     * @swagger
     * /moods:
     *   post:
     *     summary: Create a mood log
     *     tags: [Moods]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               moodTypeId:
     *                 type: number
     *     responses:
     *       201:
     *         description: Created
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     */
    this.router.post(
      "/",
      protect,
      validate(createMoodLogSchema),
      this.moodController.createMoodLog,
    );

    /**
     * @swagger
     * /moods/{id}/reactions:
     *   post:
     *     summary: Create a reaction to a mood log
     *     tags: [Moods]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: integer
     *         required: true
     *         description: Mood log ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               reactionTypeName:
     *                 type: string
     *                 enum: [seen, heart]
     *     responses:
     *       201:
     *         description: Created
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.post(
      "/:id/reactions",
      protect,
      validate(createMoodReactionSchema),
      this.moodController.createMoodReaction,
    );
  }
}

export default Mood;
