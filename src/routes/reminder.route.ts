import { Router } from "express";

import <PERSON>mind<PERSON><PERSON><PERSON>roll<PERSON> from "../controllers/reminder.controller.js";
import protect from "../middlewares/auth.middleware.js";
import validation from "../middlewares/validation.middleware.js";
import { createMoodReminderSchema } from "../validations/reminder.validation.js";

/**
 * @swagger
 * tags:
 *   name: Reminders
 *   description: Reminder management
 */

class Reminder {
  private readonly reminderController = new ReminderController();
  public readonly router = Router();

  constructor() {
    this.GET();
    this.POST();
  }

  private GET() {
    /**
     * @swagger
     * /reminders:
     *   get:
     *     summary: Get all reminders for the logged-in user
     *     tags: [Reminders]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get("/", protect, this.reminderController.getReminders);
  }

  private POST() {
    /**
     * @swagger
     * /reminders/mood:
     *   post:
     *     summary: Create a mood reminder
     *     tags: [Reminders]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               receiverUserId:
     *                 type: number
     *     responses:
     *       200:
     *         description: OK
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.post(
      "/mood",
      protect,
      validation(createMoodReminderSchema),
      this.reminderController.createMoodReminder,
    );
  }
}

export default Reminder;
