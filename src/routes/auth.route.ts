import { Router } from "express";

import AuthController from "../controllers/auth.controller.js";
import protect from "../middlewares/auth.middleware.js";
import validate from "../middlewares/validation.middleware.js";
import {
  loginSchema,
  refreshTokenSchema,
} from "../validations/auth.validation.js";

/**
 * @swagger
 * tags:
 *   name: Auth
 *   description: Authentication
 */

class Auth {
  private readonly authController = new AuthController();
  public readonly router = Router();

  constructor() {
    this.POST();
  }

  private POST() {
    /**
     * @swagger
     * /auth/login:
     *   post:
     *     summary: Login a user
     *     tags: [Auth]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               phone:
     *                 type: string
     *     responses:
     *       200:
     *         description: OK
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     */
    this.router.post(
      "/login",
      validate(loginSchema),
      this.authController.login,
    );

    /**
     * @swagger
     * /auth/logout:
     *   post:
     *     summary: Logout a user
     *     tags: [Auth]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.post("/logout", protect, this.authController.logout);

    /**
     * @swagger
     * /auth/refresh:
     *   post:
     *     summary: Refresh access token
     *     tags: [Auth]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               refreshToken:
     *                 type: string
     *     responses:
     *       200:
     *         description: OK
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     */
    this.router.post(
      "/refresh",
      validate(refreshTokenSchema),
      this.authController.refreshToken,
    );
  }
}

export default Auth;
