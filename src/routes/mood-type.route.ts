import { Router } from "express";

import MoodTypeController from "../controllers/mood-type.controller.js";

/**
 * @swagger
 * tags:
 *   name: Mood Types
 *   description: Mood type management
 */

class MoodType {
  private readonly moodTypeController = new MoodTypeController();
  public readonly router = Router();

  constructor() {
    this.GET();
  }

  private GET() {
    /**
     * @swagger
     * /mood-types:
     *   get:
     *     summary: Get all mood types
     *     tags: [Mood Types]
     *     responses:
     *       200:
     *         description: OK
     */
    this.router.get("/", this.moodTypeController.getMoodTypes);
  }
}

export default MoodType;
