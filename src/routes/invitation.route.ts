import { Router } from "express";

import InvitationController from "../controllers/invitation.controller.js";
import protect from "../middlewares/auth.middleware.js";
import validate from "../middlewares/validation.middleware.js";
import {
  createInvitationSchema,
  deleteInvitationSchema,
  updateInvitationSchema,
} from "../validations/invitation.validation.js";

/**
 * @swagger
 * tags:
 *   name: Invitations
 *   description: Invitation management
 */

class Invitation {
  private readonly invitationController = new InvitationController();
  public readonly router = Router();

  constructor() {
    this.GET();
    this.POST();
    this.PATCH();
    this.DELETE();
  }

  private GET() {
    /**
     * @swagger
     * /invitations/sent:
     *   get:
     *     summary: Get sent invitations
     *     tags: [Invitations]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get(
      "/sent",
      protect,
      this.invitationController.getSentInvitations,
    );

    /**
     * @swagger
     * /invitations/received:
     *   get:
     *     summary: Get received invitations
     *     tags: [Invitations]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get(
      "/received",
      protect,
      this.invitationController.getReceivedInvitations,
    );
  }

  private POST() {
    /**
     * @swagger
     * /invitations:
     *   post:
     *     summary: Create an invitation
     *     tags: [Invitations]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               inviteePhone:
     *                 type: string
     *               familyRelTypeId:
     *                 type: number
     *     responses:
     *       201:
     *         description: Created
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     */
    this.router.post(
      "/",
      protect,
      validate(createInvitationSchema),
      this.invitationController.createInvitation,
    );
  }

  private PATCH() {
    /**
     * @swagger
     * /invitations/{id}:
     *   patch:
     *     summary: Update an invitation status (accept or reject)
     *     tags: [Invitations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: integer
     *         required: true
     *         description: Invitation ID
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             type: object
     *             properties:
     *               status:
     *                 type: string
     *                 enum: [accepted, rejected]
     *     responses:
     *       200:
     *         description: OK
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.patch(
      "/:id",
      protect,
      validate(updateInvitationSchema),
      this.invitationController.updateInvitation,
    );
  }

  private DELETE() {
    /**
     * @swagger
     * /invitations/{id}:
     *   delete:
     *     summary: Delete an invitation
     *     tags: [Invitations]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: integer
     *         required: true
     *         description: Invitation ID
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.delete(
      "/:id",
      protect,
      validate(deleteInvitationSchema),
      this.invitationController.deleteInvitation,
    );
  }
}

export default Invitation;
