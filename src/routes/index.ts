import Auth from "./auth.route.js";
import Invitation from "./invitation.route.js";
import Mood from "./mood.route.js";
import MoodType from "./mood-type.route.js";
import Notification from "./notification.route.js";
import Reminder from "./reminder.route.js";
import Report from "./report.route.js";
import User from "./user.route.js";

const authRoute = new Auth();
const invitationRoute = new Invitation();
const moodRoute = new Mood();
const moodTypeRoute = new MoodType();
const notificationRoute = new Notification();
const reminderRoute = new Reminder();
const reportRoute = new Report();
const userRoute = new User();

export default [
  { path: "auth", router: authRoute.router },
  { path: "invitations", router: invitationRoute.router },
  { path: "moods", router: moodRoute.router },
  { path: "mood-types", router: moodTypeRoute.router },
  { path: "notifications", router: notificationRoute.router },
  { path: "reminders", router: reminderRoute.router },
  { path: "reports", router: reportRoute.router },
  { path: "users", router: userRoute.router },
];
