import { Router } from "express";

import UserController from "../controllers/user.controller.js";
import protect from "../middlewares/auth.middleware.js";
import restrictTo from "../middlewares/authorization.middleware.js";
import validation from "../middlewares/validation.middleware.js";
import {
  createUserSchema,
  getUserSchema,
  updateMeSchema,
} from "../validations/user.validation.js";

/**
 * @swagger
 * tags:
 *   name: Users
 *   description: User management
 */

class User {
  private readonly userController = new UserController();
  public readonly router = Router();

  constructor() {
    this.GET();
    this.POST();
    this.PATCH();
  }

  private GET() {
    /**
     * @swagger
     * /users:
     *   get:
     *     summary: Get all users (admin only)
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     *       403:
     *         description: Forbidden
     */
    this.router.get(
      "/",
      protect,
      restrictTo("admin"),
      this.userController.getUsers,
    );

    /**
     * @swagger
     * /users/me:
     *   get:
     *     summary: Get the logged-in user
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     */
    this.router.get("/me", protect, this.userController.getMe);

    /**
     * @swagger
     * /users/{id}:
     *   get:
     *     summary: Get a user by ID
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     parameters:
     *       - in: path
     *         name: id
     *         schema:
     *           type: integer
     *         required: true
     *         description: User ID
     *     responses:
     *       200:
     *         description: OK
     *       401:
     *         description: Unauthorized
     *       404:
     *         description: Not found
     */
    this.router.get(
      "/:id",
      protect,
      validation(getUserSchema),
      this.userController.getUser,
    );
  }

  private POST() {
    /**
     * @swagger
     * /users:
     *   post:
     *     summary: Create a new user
     *     tags: [Users]
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/User'
     *     responses:
     *       201:
     *         description: Created
     *       400:
     *         description: Bad request
     */
    this.router.post(
      "/",
      validation(createUserSchema),
      this.userController.createUser,
    );
  }

  private PATCH() {
    /**
     * @swagger
     * /users/me:
     *   patch:
     *     summary: Update the logged-in user
     *     tags: [Users]
     *     security:
     *       - bearerAuth: []
     *     requestBody:
     *       required: true
     *       content:
     *         application/json:
     *           schema:
     *             $ref: '#/components/schemas/UserUpdate'
     *     responses:
     *       200:
     *         description: OK
     *       400:
     *         description: Bad request
     *       401:
     *         description: Unauthorized
     */
    this.router.patch(
      "/me",
      protect,
      validation(updateMeSchema),
      this.userController.updateMe,
    );
  }
}

/**
 * @swagger
 * components:
 *   schemas:
 *     User:
 *       type: object
 *       properties:
 *         phone:
 *           type: string
 *         password:
 *           type: string
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         gender:
 *           type: string
 *           enum: [male, female]
 *     UserUpdate:
 *       type: object
 *       properties:
 *         firstName:
 *           type: string
 *         lastName:
 *           type: string
 *         gender:
 *           type: string
 *           enum: [male, female]
 */

export default User;
