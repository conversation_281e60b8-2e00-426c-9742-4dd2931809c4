import { drizzle } from "drizzle-orm/mysql2";

import familyRelTypes from "./schema/family-rel-types.js";
import familyRels from "./schema/family-rels.js";
import invitations from "./schema/invitations.js";
import leafLogs from "./schema/leaf-logs.js";
import leafSrcActions from "./schema/leaf-src-actions.js";
import leafSrcTables from "./schema/leaf-src-tables.js";
import moodLogs from "./schema/mood-logs.js";
import moodTypes from "./schema/mood-types.js";
import notifs from "./schema/notifs.js";
import notifSrcTables from "./schema/notif-src-tables.js";
import notifTypes from "./schema/notif-types.js";
import reactionTypes from "./schema/reaction-types.js";
import reactions from "./schema/reactions.js";
import reminderTypes from "./schema/reminder-types.js";
import reminders from "./schema/reminders.js";
import revokedTokens from "./schema/revoked-tokens.js";
import roleTypes from "./schema/role-types.js";
import users from "./schema/users.js";
import env from "../utils/env.js";

const db = drizzle({
  casing: "snake_case",
  connection: env.DB_URL,
  mode: "default",
  schema: {
    familyRelTypes,
    familyRels,
    invitations,
    leafLogs,
    leafSrcActions,
    leafSrcTables,
    moodLogs,
    moodTypes,
    notifs,
    notifSrcTables,
    notifTypes,
    reactionTypes,
    reactions,
    reminderTypes,
    reminders,
    revokedTokens,
    roleTypes,
    users,
  },
});

export default db;
