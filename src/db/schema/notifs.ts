import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  boolean,
  int,
  mysqlTable as table,
  timestamp,
} from "drizzle-orm/mysql-core";

import notifSrcTables from "./notif-src-tables.js";
import notifTypes from "./notif-types.js";
import users from "./users.js";

const notifs = table("notifs", {
  id: int().autoincrement().primaryKey(),
  notifSrcTableId: int()
    .notNull()
    .references(() => notifSrcTables.id),
  notifTypeId: int()
    .notNull()
    .references(() => notifTypes.id),
  receiverUserId: int()
    .notNull()
    .references(() => users.id),
  senderUserId: int()
    .notNull()
    .references(() => users.id),
  isRead: boolean().notNull().default(false),
  srcId: int().notNull(),
  createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  updatedAt: timestamp({ mode: "string" }).onUpdateNow(),
});

const insertNotifSchema = createInsertSchema(notifs);
const selectNotifSchema = createSelectSchema(notifs);

type InsertNotif = typeof notifs.$inferInsert;
type SelectNotif = typeof notifs.$inferSelect;

export default notifs;
export { insertNotifSchema, selectNotifSchema };
export type { InsertNotif, SelectNotif };
