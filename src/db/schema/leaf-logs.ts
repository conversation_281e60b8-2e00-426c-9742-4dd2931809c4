import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, timestamp } from "drizzle-orm/mysql-core";

import leafSrcActions from "./leaf-src-actions.js";
import leafSrcTables from "./leaf-src-tables.js";
import users from "./users.js";

const leafLogs = table("leaf_logs", {
  id: int().autoincrement().primaryKey(),
  leafSrcActionId: int()
    .notNull()
    .references(() => leafSrcActions.id),
  leafSrcTableId: int()
    .notNull()
    .references(() => leafSrcTables.id),
  userId: int()
    .notNull()
    .references(() => users.id),
  earnedLeaves: int().notNull(),
  srcId: int().notNull(),
  createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
});

const insertLeafLogSchema = createInsertSchema(leafLogs);
const selectLeafLogSchema = createSelectSchema(leafLogs);

type InsertLeafLog = typeof leafLogs.$inferInsert;
type SelectLeafLog = typeof leafLogs.$inferSelect;

export default leafLogs;
export { insertLeafLogSchema, selectLeafLogSchema };
export type { InsertLeafLog, SelectLeafLog };
