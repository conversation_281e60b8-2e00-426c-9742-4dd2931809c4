import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const notifSrcTables = table("notif_src_tables", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertNotifSrcTableSchema = createInsertSchema(notifSrcTables);
const selectNotifSrcTableSchema = createSelectSchema(notifSrcTables);

type InsertNotifSrcTable = typeof notifSrcTables.$inferInsert;
type SelectNotifSrcTable = typeof notifSrcTables.$inferSelect;

export default notifSrcTables;
export { insertNotifSrcTableSchema, selectNotifSrcTableSchema };
export type { InsertNotifSrcTable, SelectNotifSrcTable };
