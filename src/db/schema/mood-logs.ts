import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  date,
  int,
  mysqlTable as table,
  timestamp,
  uniqueIndex,
} from "drizzle-orm/mysql-core";

import moodTypes from "./mood-types.js";
import users from "./users.js";

const moodLogs = table(
  "mood_logs",
  {
    id: int().autoincrement().primaryKey(),
    moodTypeId: int()
      .notNull()
      .references(() => moodTypes.id),
    userId: int()
      .notNull()
      .references(() => users.id),
    logDate: date({ mode: "string" }).notNull(),
    createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  },
  (moodLogs) => [
    uniqueIndex("unique_mood_log").on(moodLogs.userId, moodLogs.logDate),
  ],
);

const insertMoodLogSchema = createInsertSchema(moodLogs);
const selectMoodLogSchema = createSelectSchema(moodLogs);

type InsertMoodLog = typeof moodLogs.$inferInsert;
type SelectMoodLog = typeof moodLogs.$inferSelect;

export default moodLogs;
export { insertMoodLogSchema, selectMoodLogSchema };
export type { InsertMoodLog, SelectMoodLog };
