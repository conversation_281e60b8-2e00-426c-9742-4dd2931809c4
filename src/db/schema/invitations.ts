import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  int,
  mysqlEnum,
  mysqlTable as table,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";

import familyRelTypes from "./family-rel-types.js";
import users from "./users.js";

const invitations = table("invitations", {
  id: int().autoincrement().primaryKey(),
  familyRelTypeId: int()
    .notNull()
    .references(() => familyRelTypes.id),
  userId: int()
    .notNull()
    .references(() => users.id),
  invitationStatusType: mysqlEnum([
    "accepted",
    "deleted",
    "expired",
    "joined",
    "pending",
    "rejected",
  ])
    .default("pending")
    .notNull(),
  inviteePhone: varchar({ length: 255 }).notNull(),
  createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  deletedAt: timestamp({ mode: "string" }),
  expiresAt: timestamp({ mode: "string" }).notNull(),
  lastReminderAt: timestamp({ mode: "string" }),
  updatedAt: timestamp({ mode: "string" }).onUpdateNow(),
});

const insertInvitationSchema = createInsertSchema(invitations);
const selectInvitationSchema = createSelectSchema(invitations);

type InsertInvitation = typeof invitations.$inferInsert;
type SelectInvitation = typeof invitations.$inferSelect;

export default invitations;
export { insertInvitationSchema, selectInvitationSchema };
export type { InsertInvitation, SelectInvitation };
