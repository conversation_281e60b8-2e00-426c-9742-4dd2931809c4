import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const leafSrcActions = table("leaf_src_actions", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertLeafSrcActionSchema = createInsertSchema(leafSrcActions);
const selectLeafSrcActionSchema = createSelectSchema(leafSrcActions);

type InsertLeafSrcAction = typeof leafSrcActions.$inferInsert;
type SelectLeafSrcAction = typeof leafSrcActions.$inferSelect;

export default leafSrcActions;
export { insertLeafSrcActionSchema, selectLeafSrcActionSchema };
export type { InsertLeafSrcAction, SelectLeafSrcAction };
