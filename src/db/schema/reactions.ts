import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  int,
  mysqlTable as table,
  timestamp,
  uniqueIndex,
} from "drizzle-orm/mysql-core";

import moodLogs from "./mood-logs.js";
import reactionTypes from "./reaction-types.js";
import users from "./users.js";

const reactions = table(
  "reactions",
  {
    id: int().autoincrement().primaryKey(),
    moodLogId: int()
      .notNull()
      .references(() => moodLogs.id),
    reactionTypeId: int()
      .notNull()
      .references(() => reactionTypes.id),
    userId: int()
      .notNull()
      .references(() => users.id),
    createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  },
  (reactions) => [
    uniqueIndex("unique_reaction").on(
      reactions.userId,
      reactions.moodLogId,
      reactions.reactionTypeId,
    ),
  ],
);

const insertReactionSchema = createInsertSchema(reactions);
const selectReactionSchema = createSelectSchema(reactions);

type InsertReaction = typeof reactions.$inferInsert;
type SelectReaction = typeof reactions.$inferSelect;

export default reactions;
export { insertReactionSchema, selectReactionSchema };
export type { InsertReaction, SelectReaction };
