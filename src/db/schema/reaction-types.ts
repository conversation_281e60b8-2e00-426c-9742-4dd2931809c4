import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const reactionTypes = table("reaction_types", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertReactionTypeSchema = createInsertSchema(reactionTypes);
const selectReactionTypeSchema = createSelectSchema(reactionTypes);

type InsertReactionType = typeof reactionTypes.$inferInsert;
type SelectReactionType = typeof reactionTypes.$inferSelect;

export default reactionTypes;
export { insertReactionTypeSchema, selectReactionTypeSchema };
export type { InsertReactionType, SelectReactionType };
