import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const notifTypes = table("notif_types", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertNotifTypeSchema = createInsertSchema(notifTypes);
const selectNotifTypeSchema = createSelectSchema(notifTypes);

type InsertNotifType = typeof notifTypes.$inferInsert;
type SelectNotifType = typeof notifTypes.$inferSelect;

export default notifTypes;
export { insertNotifTypeSchema, selectNotifTypeSchema };
export type { InsertNotifType, SelectNotifType };
