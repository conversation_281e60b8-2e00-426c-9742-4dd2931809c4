import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  type AnyMySqlColumn,
  int,
  mysqlTable as table,
  varchar,
} from "drizzle-orm/mysql-core";

const familyRelTypes = table("family_rel_types", {
  id: int().autoincrement().primaryKey(),
  inverse_female: int()
    .notNull()
    .references((): AnyMySqlColumn => familyRelTypes.id),
  inverse_male: int()
    .notNull()
    .references((): AnyMySqlColumn => familyRelTypes.id),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertFamilyRelTypeSchema = createInsertSchema(familyRelTypes);
const selectFamilyRelTypeSchema = createSelectSchema(familyRelTypes);

type InsertFamilyRelType = typeof familyRelTypes.$inferInsert;
type SelectFamilyRelType = typeof familyRelTypes.$inferSelect;

export default familyRelTypes;
export { insertFamilyRelTypeSchema, selectFamilyRelTypeSchema };
export type { InsertFamilyRelType, SelectFamilyRelType };
