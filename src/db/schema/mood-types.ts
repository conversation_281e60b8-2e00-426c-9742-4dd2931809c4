import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const moodTypes = table("mood_types", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertMoodTypeSchema = createInsertSchema(moodTypes);
const selectMoodTypeSchema = createSelectSchema(moodTypes);

type InsertMoodType = typeof moodTypes.$inferInsert;
type SelectMoodType = typeof moodTypes.$inferSelect;

export default moodTypes;
export { insertMoodTypeSchema, selectMoodTypeSchema };
export type { InsertMoodType, SelectMoodType };
