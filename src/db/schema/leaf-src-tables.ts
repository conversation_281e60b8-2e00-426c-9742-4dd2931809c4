import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const leafSrcTables = table("leaf_src_tables", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertLeafSrcTableSchema = createInsertSchema(leafSrcTables);
const selectLeafSrcTableSchema = createSelectSchema(leafSrcTables);

type InsertLeafSrcTable = typeof leafSrcTables.$inferInsert;
type SelectLeafSrcTable = typeof leafSrcTables.$inferSelect;

export default leafSrcTables;
export { insertLeafSrcTableSchema, selectLeafSrcTableSchema };
export type { InsertLeafSrcTable, SelectLeafSrcTable };
