import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const roleTypes = table("role_types", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertRoleTypeSchema = createInsertSchema(roleTypes);
const selectRoleTypeSchema = createSelectSchema(roleTypes);

type InsertRoleType = typeof roleTypes.$inferInsert;
type SelectRoleType = typeof roleTypes.$inferSelect;

export default roleTypes;
export { insertRoleTypeSchema, selectRoleTypeSchema };
export type { InsertRoleType, SelectRoleType };
