import {
  date,
  int,
  mysqlEnum,
  mysqlTable as table,
  timestamp,
  varchar,
} from "drizzle-orm/mysql-core";

import { createInsertSchema, createSelectSchema } from "drizzle-zod";

import roleTypes from "./role-types.js";

const users = table("users", {
  id: int().autoincrement().primaryKey(),
  roleTypeId: int()
    .notNull()
    .references(() => roleTypes.id),
  birthday: date().notNull(),
  city: varchar({ length: 255 }).notNull(),
  firstName: varchar({ length: 255 }).notNull(),
  gender: mysqlEnum(["آقا", "خانم"]).notNull(),
  lastName: varchar({ length: 255 }).notNull(),
  phone: varchar({ length: 255 }).notNull().unique(),
  profilePictureUrl: varchar({ length: 255 }),
  province: varchar({ length: 255 }).notNull(),
  refreshToken: varchar({ length: 255 }),
  totalLeavesCount: int().notNull().default(0),
  createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  updatedAt: timestamp({ mode: "string" }).onUpdateNow(),
});

const insertUserSchema = createInsertSchema(users);
const selectUserSchema = createSelectSchema(users);

type InsertUser = typeof users.$inferInsert;
type SelectUser = typeof users.$inferSelect;

export default users;
export { insertUserSchema, selectUserSchema };
export type { InsertUser, SelectUser };
