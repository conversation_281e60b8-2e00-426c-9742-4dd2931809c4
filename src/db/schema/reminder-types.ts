import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, varchar } from "drizzle-orm/mysql-core";

const reminderTypes = table("reminder_types", {
  id: int().autoincrement().primaryKey(),
  name: varchar({ length: 255 }).notNull().unique(),
});

const insertReminderTypeSchema = createInsertSchema(reminderTypes);
const selectReminderTypeSchema = createSelectSchema(reminderTypes);

type InsertReminderType = typeof reminderTypes.$inferInsert;
type SelectReminderType = typeof reminderTypes.$inferSelect;

export default reminderTypes;
export { insertReminderTypeSchema, selectReminderTypeSchema };
export type { InsertReminderType, SelectReminderType };
