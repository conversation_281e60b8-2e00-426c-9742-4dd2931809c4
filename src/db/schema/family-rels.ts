import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { int, mysqlTable as table, timestamp } from "drizzle-orm/mysql-core";

import familyRelTypes from "./family-rel-types.js";
import users from "./users.js";

const familyRels = table("family_rels", {
  id: int().autoincrement().primaryKey(),
  familyRelTypeId: int()
    .notNull()
    .references(() => familyRelTypes.id),
  fromUserId: int()
    .notNull()
    .references(() => users.id),
  toUserId: int()
    .notNull()
    .references(() => users.id),
  createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  deletedAt: timestamp({ mode: "string" }),
});

const insertFamilyRelSchema = createInsertSchema(familyRels);
const selectFamilyRelSchema = createSelectSchema(familyRels);

type InsertFamilyRel = typeof familyRels.$inferInsert;
type SelectFamilyRel = typeof familyRels.$inferSelect;

export default familyRels;
export { insertFamilyRelSchema, selectFamilyRelSchema };
export type { InsertFamilyRel, SelectFamilyRel };
