import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import {
  date,
  int,
  mysqlTable as table,
  timestamp,
  uniqueIndex,
} from "drizzle-orm/mysql-core";

import reminderTypes from "./reminder-types.js";
import users from "./users.js";

const reminders = table(
  "reminders",
  {
    id: int().autoincrement().primaryKey(),
    receiverUserId: int()
      .notNull()
      .references(() => users.id),
    reminderTypeId: int()
      .notNull()
      .references(() => reminderTypes.id),
    senderUserId: int()
      .notNull()
      .references(() => users.id),
    reminderDate: date({ mode: "string" }).notNull(),
    createdAt: timestamp({ mode: "string" }).defaultNow().notNull(),
  },
  (reminders) => [
    uniqueIndex("unique_mood_reminder").on(
      reminders.senderUserId,
      reminders.receiverUserId,
      reminders.reminderTypeId,
      reminders.reminderDate,
    ),
  ],
);

const insertReminderSchema = createInsertSchema(reminders);
const selectReminderSchema = createSelectSchema(reminders);

type InsertReminder = typeof reminders.$inferInsert;
type SelectReminder = typeof reminders.$inferSelect;

export default reminders;
export { insertReminderSchema, selectReminderSchema };
export type { InsertReminder, SelectReminder };
