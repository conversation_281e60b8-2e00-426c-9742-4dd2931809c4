CREATE TABLE `family_rel_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`inverse_female` int NOT NULL,
	`inverse_male` int NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `family_rel_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `family_rel_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `family_rels` (
	`id` int AUTO_INCREMENT NOT NULL,
	`family_rel_type_id` int NOT NULL,
	`from_user_id` int NOT NULL,
	`to_user_id` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`deleted_at` timestamp,
	CONSTRAINT `family_rels_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `invitations` (
	`id` int AUTO_INCREMENT NOT NULL,
	`family_rel_type_id` int NOT NULL,
	`user_id` int NOT NULL,
	`invitation_status_type` enum('accepted','deleted','expired','joined','pending','rejected') NOT NULL DEFAULT 'pending',
	`invitee_phone` varchar(255) NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`deleted_at` timestamp,
	`expires_at` timestamp NOT NULL,
	`last_reminder_at` timestamp,
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `invitations_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `leaf_logs` (
	`id` int AUTO_INCREMENT NOT NULL,
	`leaf_src_action_id` int NOT NULL,
	`leaf_src_table_id` int NOT NULL,
	`user_id` int NOT NULL,
	`earned_leaves` int NOT NULL,
	`src_id` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `leaf_logs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `leaf_src_actions` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `leaf_src_actions_id` PRIMARY KEY(`id`),
	CONSTRAINT `leaf_src_actions_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `leaf_src_tables` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `leaf_src_tables_id` PRIMARY KEY(`id`),
	CONSTRAINT `leaf_src_tables_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `mood_logs` (
	`id` int AUTO_INCREMENT NOT NULL,
	`mood_type_id` int NOT NULL,
	`user_id` int NOT NULL,
	`log_date` date NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `mood_logs_id` PRIMARY KEY(`id`),
	CONSTRAINT `unique_mood_log` UNIQUE(`user_id`,`log_date`)
);
--> statement-breakpoint
CREATE TABLE `mood_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `mood_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `mood_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `notif_src_tables` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `notif_src_tables_id` PRIMARY KEY(`id`),
	CONSTRAINT `notif_src_tables_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `notif_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `notif_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `notif_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `notifs` (
	`id` int AUTO_INCREMENT NOT NULL,
	`notif_src_table_id` int NOT NULL,
	`notif_type_id` int NOT NULL,
	`receiver_user_id` int NOT NULL,
	`sender_user_id` int NOT NULL,
	`is_read` boolean NOT NULL DEFAULT false,
	`src_id` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `notifs_id` PRIMARY KEY(`id`)
);
--> statement-breakpoint
CREATE TABLE `reaction_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `reaction_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `reaction_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `reactions` (
	`id` int AUTO_INCREMENT NOT NULL,
	`mood_log_id` int NOT NULL,
	`reaction_type_id` int NOT NULL,
	`user_id` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `reactions_id` PRIMARY KEY(`id`),
	CONSTRAINT `unique_reaction` UNIQUE(`user_id`,`mood_log_id`,`reaction_type_id`)
);
--> statement-breakpoint
CREATE TABLE `reminder_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `reminder_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `reminder_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `reminders` (
	`id` int AUTO_INCREMENT NOT NULL,
	`receiver_user_id` int NOT NULL,
	`reminder_type_id` int NOT NULL,
	`sender_user_id` int NOT NULL,
	`reminder_date` date NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `reminders_id` PRIMARY KEY(`id`),
	CONSTRAINT `unique_mood_reminder` UNIQUE(`sender_user_id`,`receiver_user_id`,`reminder_type_id`,`reminder_date`)
);
--> statement-breakpoint
CREATE TABLE `revoked_tokens` (
	`token` varchar(256) NOT NULL,
	`user_id` int NOT NULL,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	CONSTRAINT `revoked_tokens_token` PRIMARY KEY(`token`)
);
--> statement-breakpoint
CREATE TABLE `role_types` (
	`id` int AUTO_INCREMENT NOT NULL,
	`name` varchar(255) NOT NULL,
	CONSTRAINT `role_types_id` PRIMARY KEY(`id`),
	CONSTRAINT `role_types_name_unique` UNIQUE(`name`)
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` int AUTO_INCREMENT NOT NULL,
	`role_type_id` int NOT NULL,
	`birthday` date NOT NULL,
	`city` varchar(255) NOT NULL,
	`first_name` varchar(255) NOT NULL,
	`gender` enum('آقا','خانم') NOT NULL,
	`last_name` varchar(255) NOT NULL,
	`phone` varchar(255) NOT NULL,
	`profile_picture_url` varchar(255),
	`province` varchar(255) NOT NULL,
	`refresh_token` varchar(255),
	`total_leaves_count` int NOT NULL DEFAULT 0,
	`created_at` timestamp NOT NULL DEFAULT (now()),
	`updated_at` timestamp ON UPDATE CURRENT_TIMESTAMP,
	CONSTRAINT `users_id` PRIMARY KEY(`id`),
	CONSTRAINT `users_phone_unique` UNIQUE(`phone`)
);
--> statement-breakpoint
ALTER TABLE `family_rel_types` ADD CONSTRAINT `family_rel_types_inverse_female_family_rel_types_id_fk` FOREIGN KEY (`inverse_female`) REFERENCES `family_rel_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `family_rel_types` ADD CONSTRAINT `family_rel_types_inverse_male_family_rel_types_id_fk` FOREIGN KEY (`inverse_male`) REFERENCES `family_rel_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `family_rels` ADD CONSTRAINT `family_rels_family_rel_type_id_family_rel_types_id_fk` FOREIGN KEY (`family_rel_type_id`) REFERENCES `family_rel_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `family_rels` ADD CONSTRAINT `family_rels_from_user_id_users_id_fk` FOREIGN KEY (`from_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `family_rels` ADD CONSTRAINT `family_rels_to_user_id_users_id_fk` FOREIGN KEY (`to_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `invitations` ADD CONSTRAINT `invitations_family_rel_type_id_family_rel_types_id_fk` FOREIGN KEY (`family_rel_type_id`) REFERENCES `family_rel_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `invitations` ADD CONSTRAINT `invitations_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `leaf_logs` ADD CONSTRAINT `leaf_logs_leaf_src_action_id_leaf_src_actions_id_fk` FOREIGN KEY (`leaf_src_action_id`) REFERENCES `leaf_src_actions`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `leaf_logs` ADD CONSTRAINT `leaf_logs_leaf_src_table_id_leaf_src_tables_id_fk` FOREIGN KEY (`leaf_src_table_id`) REFERENCES `leaf_src_tables`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `leaf_logs` ADD CONSTRAINT `leaf_logs_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `mood_logs` ADD CONSTRAINT `mood_logs_mood_type_id_mood_types_id_fk` FOREIGN KEY (`mood_type_id`) REFERENCES `mood_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `mood_logs` ADD CONSTRAINT `mood_logs_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `notifs` ADD CONSTRAINT `notifs_notif_src_table_id_notif_src_tables_id_fk` FOREIGN KEY (`notif_src_table_id`) REFERENCES `notif_src_tables`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `notifs` ADD CONSTRAINT `notifs_notif_type_id_notif_types_id_fk` FOREIGN KEY (`notif_type_id`) REFERENCES `notif_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `notifs` ADD CONSTRAINT `notifs_receiver_user_id_users_id_fk` FOREIGN KEY (`receiver_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `notifs` ADD CONSTRAINT `notifs_sender_user_id_users_id_fk` FOREIGN KEY (`sender_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reactions` ADD CONSTRAINT `reactions_mood_log_id_mood_logs_id_fk` FOREIGN KEY (`mood_log_id`) REFERENCES `mood_logs`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reactions` ADD CONSTRAINT `reactions_reaction_type_id_reaction_types_id_fk` FOREIGN KEY (`reaction_type_id`) REFERENCES `reaction_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reactions` ADD CONSTRAINT `reactions_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reminders` ADD CONSTRAINT `reminders_receiver_user_id_users_id_fk` FOREIGN KEY (`receiver_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reminders` ADD CONSTRAINT `reminders_reminder_type_id_reminder_types_id_fk` FOREIGN KEY (`reminder_type_id`) REFERENCES `reminder_types`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `reminders` ADD CONSTRAINT `reminders_sender_user_id_users_id_fk` FOREIGN KEY (`sender_user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `revoked_tokens` ADD CONSTRAINT `revoked_tokens_user_id_users_id_fk` FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE `users` ADD CONSTRAINT `users_role_type_id_role_types_id_fk` FOREIGN KEY (`role_type_id`) REFERENCES `role_types`(`id`) ON DELETE no action ON UPDATE no action;