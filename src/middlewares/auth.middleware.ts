const { JsonWebTokenError, TokenExpiredError } = jwt;

import { type NextFunction, type Request, type Response } from "express";
import jwt from "jsonwebtoken";
import { eq } from "drizzle-orm";

import db from "../db/index.js";
import revokedTokens from "../db/schema/revoked-tokens.js";
import env from "../utils/env.js";
import { UnauthorizedError } from "../utils/errors.js";

const protect = async (req: Request, _res: Response, next: NextFunction) => {
  let token: string | undefined;

  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith("Bearer")
  ) {
    token = req.headers.authorization.split(" ")[1];
  }

  if (!token) {
    return next(
      new UnauthorizedError(
        "You are not logged in. Please log in to get access",
      ),
    );
  }

  const isRevoked = await db.query.revokedTokens.findFirst({
    where: eq(revokedTokens.token, token),
  });

  if (isRevoked) {
    return next(new UnauthorizedError("Token has been revoked"));
  }

  try {
    const decoded = jwt.verify(token, env.JWT_SECRET) as typeof req.user;
    req.user = decoded;
    next();
  } catch (err) {
    if (err instanceof TokenExpiredError) {
      return next(new UnauthorizedError("Token has expired"));
    }
    if (err instanceof JsonWebTokenError) {
      return next(new UnauthorizedError("Invalid token"));
    }
    next(err);
  }
};

export default protect;
