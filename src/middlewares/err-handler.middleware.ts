import { type NextFunction, type Request, type Response } from "express";
import z, { ZodError } from "zod";

import { AppError } from "../utils/errors.js";

function errHandler(
  err: unknown,
  _req: Request,
  res: Response,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  _next: NextFunction,
) {
  if (err instanceof AppError) {
    console.error(`APP_ERROR: ${err.name} - ${err.message}`);

    return res.fail(err.message, { error: { code: err.name } }, err.statusCode);
  }

  if (err instanceof ZodError) {
    console.error("VALIDATION_ERROR:", z.treeifyError(err));

    return res.fail("Validation error", {
      error: z.flattenError(err).fieldErrors,
    });
  }

  console.error("UNHANDLED_ERROR:", err);

  res.fail("Internal server error", {}, 500);
}

export default errHandler;
