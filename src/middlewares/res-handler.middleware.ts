import { type NextFunction, type Request, type Response } from "express";

function resHandler(_req: Request, res: Response, next: NextFunction) {
  res.success = (
    message = "",
    data: Record<string, unknown> = {},
    statusCode = 200,
  ) => {
    res.status(statusCode).json({
      success: true,
      message,
      data,
      statusCode,
    });
  };

  res.fail = (
    message = "",
    data: Record<string, unknown> = {},
    statusCode = 400,
  ) => {
    res.status(statusCode).json({
      success: false,
      message,
      data,
      statusCode,
    });
  };

  next();
}

export default resHandler;
