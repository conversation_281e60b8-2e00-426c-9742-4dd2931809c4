import { type NextFunction, type Request, type Response } from "express";

import { ForbiddenError, UnauthorizedError } from "../utils/errors.js";

function restrictTo(...allowedRoles: Array<UserRole>) {
  return (req: Request, _res: Response, next: NextFunction) => {
    const user = req.user;

    if (!user) {
      throw new UnauthorizedError("User not found. This route is protected");
    }

    if (!allowedRoles.includes(user.role)) {
      throw new ForbiddenError(
        "You do not have permission to perform this action.",
      );
    }

    next();
  };
}

export default restrictTo;
