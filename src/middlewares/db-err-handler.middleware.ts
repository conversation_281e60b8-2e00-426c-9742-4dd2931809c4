import { DrizzleError } from "drizzle-orm";
import { type NextFunction, type Request, type Response } from "express";

import { DatabaseError } from "../utils/errors.js";

// ############################################################################
// # Types
// ############################################################################

type MySQLError = Error & {
  code?: string | number;
  errno?: number;
  fatal?: boolean;
  sql?: string;
  sqlMessage?: string;
  sqlState?: string;
};

type DrizzleErrorWithCause = Error & {
  cause?: MySQLError;
};

// ############################################################################
// # MySQL Error Codes
// ############################################################################

const MYSQL_ERROR_CODES = {
  ER_ACCESS_DENIED_ERROR: 1045,
  ER_BAD_FIELD_ERROR: 1054,
  ER_DATA_TOO_LONG: 1406,
  ER_DUP_ENTRY: 1062,
  ER_LOCK_DEADLOCK: 1213,
  ER_LOCK_WAIT_TIMEOUT: 1205,
  ER_NO_REFERENCED_ROW_2: 1452,
  ER_PARSE_ERROR: 1064,
  ER_ROW_IS_REFERENCED_2: 1451,
  ER_TRUNCATED_WRONG_VALUE: 1292,
  ER_UNKNOWN_TABLE: 1146,
  CONNECTION_LOST: "PROTOCOL_CONNECTION_LOST",
  ECONNREFUSED: "ECONNREFUSED",
  ENOTFOUND: "ENOTFOUND",
  TIMEOUT: "ETIMEDOUT",
} as const;

// ############################################################################
// # Utility Functions
// ############################################################################

function extractDuplicateField(message: string) {
  const match = message.match(/for key '([^']+)'/);

  return match ? match[1].split(".").pop() : undefined;
}

function extractForeignKeyField(message: string) {
  const match = message.match(/FOREIGN KEY \(`([^`]+)`\)/);

  return match ? match[1] : undefined;
}

// ############################################################################
// # MySQL Error Handler
// ############################################################################

function handleMySQLError(error: MySQLError) {
  const { code, errno, message } = error;

  switch (errno || code) {
    case MYSQL_ERROR_CODES.ER_ACCESS_DENIED_ERROR:
      return new DatabaseError("Database access denied", 500, "ACCESS_DENIED");

    case MYSQL_ERROR_CODES.ER_BAD_FIELD_ERROR:
      return new DatabaseError("Invalid field in query", 400, "INVALID_FIELD");

    case MYSQL_ERROR_CODES.ER_DATA_TOO_LONG:
      return new DatabaseError("Data too long for field", 400, "DATA_TOO_LONG");

    case MYSQL_ERROR_CODES.ER_DUP_ENTRY:
      const duplicateField = extractDuplicateField(message);

      return new DatabaseError(
        duplicateField
          ? `A record with this ${duplicateField} already exists`
          : "A record with these values already exists",
        409,
        "DUPLICATE_ENTRY",
        duplicateField,
      );

    case MYSQL_ERROR_CODES.ER_LOCK_DEADLOCK:
      return new DatabaseError("Database deadlock detected", 503, "DEADLOCK");

    case MYSQL_ERROR_CODES.ER_LOCK_WAIT_TIMEOUT:
      return new DatabaseError(
        "Database operation timed out due to lock wait",
        503,
        "LOCK_TIMEOUT",
      );

    case MYSQL_ERROR_CODES.ER_NO_REFERENCED_ROW_2:
      const foreignKeyField = extractForeignKeyField(message);

      return new DatabaseError(
        foreignKeyField
          ? `Invalid ${foreignKeyField} - referenced record does not exist`
          : "Referenced record does not exist",
        400,
        "FOREIGN_KEY_CONSTRAINT",
        foreignKeyField,
      );

    case MYSQL_ERROR_CODES.ER_PARSE_ERROR:
      return new DatabaseError("SQL syntax error", 500, "SQL_SYNTAX_ERROR");

    case MYSQL_ERROR_CODES.ER_ROW_IS_REFERENCED_2:
      return new DatabaseError(
        "Cannot delete record - it is referenced by other records",
        409,
        "FOREIGN_KEY_REFERENCED",
      );

    case MYSQL_ERROR_CODES.ER_TRUNCATED_WRONG_VALUE:
      return new DatabaseError(
        "Invalid data format",
        400,
        "INVALID_DATA_FORMAT",
      );

    case MYSQL_ERROR_CODES.ER_UNKNOWN_TABLE:
      return new DatabaseError("Table does not exist", 500, "TABLE_NOT_FOUND");

    case MYSQL_ERROR_CODES.CONNECTION_LOST:
    case MYSQL_ERROR_CODES.ECONNREFUSED:
      return new DatabaseError(
        "Database connection lost",
        503,
        "CONNECTION_ERROR",
      );

    case MYSQL_ERROR_CODES.ENOTFOUND:
    case MYSQL_ERROR_CODES.TIMEOUT:
      return new DatabaseError(
        "Database connection timeout",
        503,
        "CONNECTION_TIMEOUT",
      );

    default:
      return new DatabaseError("Database operation failed", 500, "MYSQL_ERROR");
  }
}

// ############################################################################
// # Database Error Handler
// ############################################################################

function handleDatabaseError(error: unknown) {
  if (error instanceof DrizzleError) {
    return new DatabaseError("Database operation failed", 500, "DRIZZLE_ERROR");
  }

  if (error instanceof Error && "cause" in error) {
    const drizzleError = error as DrizzleErrorWithCause;

    if (drizzleError.cause) {
      return handleMySQLError(drizzleError.cause);
    }
  }

  if (
    error &&
    typeof error === "object" &&
    ("code" in error || "errno" in error)
  ) {
    return handleMySQLError(error as MySQLError);
  }

  if (error instanceof Error) {
    return new DatabaseError(
      "An unexpected database error occurred",
      500,
      "UNKNOWN_DB_ERROR",
    );
  }

  return new DatabaseError(
    "An unknown database error occurred",
    500,
    "UNKNOWN_ERROR",
  );
}

// ############################################################################
// # Middleware
// ############################################################################

function dbErrHandler(
  err: unknown,
  _req: Request,
  res: Response,
  next: NextFunction,
) {
  if (
    err instanceof DatabaseError ||
    err instanceof DrizzleError ||
    (err &&
      typeof err === "object" &&
      ("cause" in err || "code" in err || "errno" in err))
  ) {
    const dbError =
      err instanceof DatabaseError ? err : handleDatabaseError(err);

    console.error("DATABASE_ERROR:", {
      message: dbError.message,
      statusCode: dbError.statusCode,
      code: dbError.code,
      field: dbError.field,
      stack: dbError.stack,
    });

    return res.fail(
      dbError.message,
      {
        error: {
          code: dbError.code,
          ...(dbError.field && { field: dbError.field }),
        },
      },
      dbError.statusCode,
    );
  }

  next(err);
}

export default dbErrHandler;
